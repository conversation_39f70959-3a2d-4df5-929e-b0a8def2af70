import openmeteo_requests
import pandas as pd
import numpy as np
import requests_cache
from retry_requests import retry
import sys
from datetime import datetime, timedelta
import traceback
import os

# Enable more verbose output
print("Starting openmeteo_demand.py script...")
print(f"Current working directory: {os.getcwd()}")
print(f"Files in current directory: {os.listdir('.')}")

# Function to convert Turkish characters to English equivalents
def convert_turkish_to_english(text):
    if not isinstance(text, str):
        return text
        
    turkish_chars = {
        'ı': 'i', 'İ': 'I',
        'ğ': 'g', 'Ğ': 'G',
        'ü': 'u', 'Ü': 'U',
        'ş': 's', 'Ş': 'S',
        'ö': 'o', 'Ö': 'O',
        'ç': 'c', 'Ç': 'C'
    }
    
    for turkish, english in turkish_chars.items():
        text = text.replace(turkish, english)
    
    return text

try:
    print("Setting up OpenMeteo API client...")
    # Setup the Open-Meteo API client with cache and retry on error
    cache_session = requests_cache.CachedSession('.cache', expire_after = -1)
    retry_session = retry(cache_session, retries = 5, backoff_factor = 0.2)
    openmeteo = openmeteo_requests.Client(session = retry_session)
    print("OpenMeteo API client setup complete")

    # Check if file exists
    if not os.path.exists("all_cities_population.xlsx"):
        print("ERROR: all_cities_population.xlsx not found in current directory")
        print(f"Current directory: {os.getcwd()}")
        print(f"Files in directory: {os.listdir('.')}")
        sys.exit(1)
        
    # Load the cities data
    print("Loading cities population data...")
    cities_df = pd.read_excel("all_cities_population.xlsx")
    print(f"Cities population data loaded successfully. Shape: {cities_df.shape}")
    print(f"Columns: {cities_df.columns.tolist()}")
    print(f"First few rows: {cities_df.head().to_dict()}")
    
    # Check if Lat and Lon columns exist
    if 'Lat' not in cities_df.columns or 'Lon' not in cities_df.columns:
        print(f"ERROR: Required columns 'Lat' and 'Lon' not found in the Excel file.")
        print(f"Available columns: {cities_df.columns.tolist()}")
        sys.exit(1)
    
    # Convert Turkish characters in city names to English equivalents
    cities_df['City'] = cities_df['City'].apply(convert_turkish_to_english)
    print("Converted Turkish characters in city names to English equivalents")
    
    # Sort by population and filter cities with over 1 million population
    cities_df = cities_df.sort_values(by='Population', ascending=False)
    cities_df = cities_df[cities_df['Population'] > 1000000]
    print(f"Found {len(cities_df)} cities with population over 1 million")
    
    # Use the Lat and Lon columns directly from the Excel file
    city_names = cities_df['City'].tolist()
    lat_list = cities_df['Lat'].tolist()
    lon_list = cities_df['Lon'].tolist()
    
    print(f"Using coordinates from Excel file for {len(city_names)} cities:")
    for i, city in enumerate(city_names):
        print(f"  {city}: Lat={lat_list[i]}, Lon={lon_list[i]}")
    
except Exception as e:
    print(f"Error in initial setup: {e}")
    print("Traceback:")
    traceback.print_exc()
    print("Please ensure all_cities_population.xlsx exists with Lat and Lon columns")
    sys.exit(1)

# Define the weather variables we want to retrieve
request_columns = [
    "temperature_2m", 
    "relative_humidity_2m", 
    "apparent_temperature", 
    "precipitation", 
    "rain", 
    "snowfall", 
    "wind_speed_10m"
]

# Calculate two_days_ago's date for the end_date
two_days_ago = (datetime.now() - timedelta(days=2)).strftime('%Y-%m-%d')
print(f"Setting end date to two_days_ago: {two_days_ago}")

# Make sure all required weather variables are listed here
# The order of variables in hourly or daily is important to assign them correctly below
url = "https://archive-api.open-meteo.com/v1/archive"

start_date = "2023-01-01"
params = {
    "latitude": lat_list,
    "longitude": lon_list,
    "start_date": start_date,
    "end_date": two_days_ago,
    "hourly": request_columns,
    "timezone": "Europe/Istanbul"  # Turkey's timezone
}

try:
    print(f"Requesting weather data from OpenMeteo API for {len(city_names)} cities...")
    responses = openmeteo.weather_api(url, params=params)
    print(f"Received responses for {len(responses)} cities")
    
    # First, get the date range from the first city to use as the index
    first_response = responses[0]
    hourly = first_response.Hourly()
    date_range = pd.date_range(
        start = pd.to_datetime(hourly.Time(), unit = "s", utc = True),
        end = pd.to_datetime(hourly.TimeEnd(), unit = "s", utc = True),
        freq = pd.Timedelta(seconds = hourly.Interval()),
        inclusive = "left"
    )
    
    # Add 3 hours to all dates to adjust for local time differences
    date_range = date_range + pd.Timedelta(hours=3)
    
    # Remove timezone for MySQL compatibility
    date_range = date_range.tz_localize(None)
    
    print(f"Date range: {date_range[0]} to {date_range[-1]}, {len(date_range)} hours")
    
    # Create a dataframe with dates as the index
    combined_df = pd.DataFrame(index=date_range)
    combined_df.index.name = 'date_time'
    
    # Process each location and add columns for each city
    for i, response in enumerate(responses):
        city_name = city_names[i]
        print(f"\nProcessing data for {city_name}")
        print(f"Coordinates {response.Latitude()}°N {response.Longitude()}°E")
        
        # Process hourly data. The order of variables needs to be the same as requested.
        hourly = response.Hourly()
        
        # Create a dictionary to store all variables
        hourly_data = {}
        
        # Process each variable dynamically based on request_columns
        for idx, var_name in enumerate(params["hourly"]):
            hourly_data[var_name] = hourly.Variables(idx).ValuesAsNumpy()
        
        # Add columns for this city with the city name as suffix
        for var_name in params["hourly"]:
            combined_df[f"{var_name}_{city_name}"] = hourly_data[var_name]
        
        print(f"Added data for {city_name} ({len(hourly_data[params['hourly'][0]])} records)")
    
    # Reset index to make Date a column
    combined_df = combined_df.reset_index()
    print(f"\nCombined data for all cities: {len(combined_df)} rows, {len(combined_df.columns)} columns")

    # Create output directory if it doesn't exist
    output_dir = "../Dataset/weather"
    os.makedirs(output_dir, exist_ok=True)

    # Save to CSV in the weather dataset directory
    output_file = os.path.join(output_dir, "openmeteo_weather_data.csv")
    combined_df.to_csv(output_file, index=False)
    print(f"Saved weather data to {output_file}")

    # Also save backup locally for debugging
    combined_df.to_csv("combined_df.csv", index=False)
    print("Saved backup to combined_df.csv")
except Exception as e:
    print(f"Error retrieving or processing weather data: {e}")
    print("Traceback:")
    traceback.print_exc()
    sys.exit(1)

try:
    # Create a summary dataframe with average temperature and other metrics for each city
    print("Creating summary dataframe...")
    summary_data = {
        'city': [],
        'avg_temperature_2m': [],
        'max_temperature_2m': [],
        'min_temperature_2m': [],
        'avg_humidity': [],
        'avg_precipitation': [],
        'total_precipitation': [],
        'avg_wind_speed_10m': [],
        'max_wind_speed_10m': [],
        'latitude': [],
        'longitude': []
    }
    
    for i, city in enumerate(city_names):
        summary_data['city'].append(city)
        summary_data['avg_temperature_2m'].append(round(combined_df[f"temperature_2m_{city}"].mean(), 2))
        summary_data['max_temperature_2m'].append(round(combined_df[f"temperature_2m_{city}"].max(), 2))
        summary_data['min_temperature_2m'].append(round(combined_df[f"temperature_2m_{city}"].min(), 2))
        summary_data['avg_humidity'].append(round(combined_df[f"relative_humidity_2m_{city}"].mean(), 2))
        summary_data['avg_precipitation'].append(round(combined_df[f"precipitation_{city}"].mean(), 2))
        summary_data['total_precipitation'].append(round(combined_df[f"precipitation_{city}"].sum(), 2))
        summary_data['avg_wind_speed_10m'].append(round(combined_df[f"wind_speed_10m_{city}"].mean(), 2))
        summary_data['max_wind_speed_10m'].append(round(combined_df[f"wind_speed_10m_{city}"].max(), 2))
        summary_data['latitude'].append(params['latitude'][i])
        summary_data['longitude'].append(params['longitude'][i])
    
    summary_df = pd.DataFrame(summary_data)
    print(f"Created summary dataframe with {len(summary_df)} rows")

    # Save summary data to CSV in the weather dataset directory
    summary_output_file = os.path.join(output_dir, "openmeteo_weather_summary.csv")
    summary_df.to_csv(summary_output_file, index=False)
    print(f"Saved weather summary to {summary_output_file}")
    
except Exception as e:
    print(f"Error creating summary dataframe: {e}")
    print("Traceback:")
    traceback.print_exc()
    sys.exit(1)

# Data processing completed - CSV files have been saved above
print("\nWeather data processing completed successfully!")

print("\nAll weather data has been saved to CSV files")
print("The output contains:")
print(f"- openmeteo_weather_data.csv with {len(combined_df)} rows and {len(combined_df.columns)} columns in ../Dataset/weather/ directory")
print(f"- openmeteo_weather_summary.csv with statistics for each of the {len(city_names)} cities in ../Dataset/weather/ directory")

# Success
sys.exit(0)
