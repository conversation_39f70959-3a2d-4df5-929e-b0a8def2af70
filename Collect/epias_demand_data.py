import seffaflik2 as sf
from datetime import datetime, timedelta
import pandas as pd
import sys
import time
import traceback
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

print("Starting epias_demand.py script...")
print(f"Current working directory: {os.getcwd()}")
print(f"Files in current directory: {os.listdir('.')}")

# EPIAS API credentials from environment variables
myMail = os.getenv('EPIAS_EMAIL')
myPsw = os.getenv('EPIAS_PASSWORD')

# Validate that credentials are loaded
if not myMail or not myPsw:
    print("Error: EPIAS credentials not found in environment variables.")
    print("Please ensure EPIAS_EMAIL and EPIAS_PASSWORD are set in the .env file.")
    sys.exit(1)
yesterday_string = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')

# Function to split date range into yearly chunks
def get_yearly_chunks(start_date, end_date):
    start = datetime.strptime(start_date, '%Y-%m-%d')
    end = datetime.strptime(end_date, '%Y-%m-%d')
    
    chunks = []
    
    # Align to start of year if not already
    if start.month != 1 or start.day != 1:
        year_end = datetime(start.year, 12, 31)
        if year_end > end:
            # If end date is in the same year, just use the whole period
            chunks.append((start.strftime('%Y-%m-%d'), end.strftime('%Y-%m-%d')))
            return chunks
        
        # Add partial first year
        chunks.append((start.strftime('%Y-%m-%d'), year_end.strftime('%Y-%m-%d')))
        start = datetime(start.year + 1, 1, 1)
    
    # Process full years
    while start < end:
        year_end = datetime(start.year, 12, 31)
        if year_end > end:
            year_end = end
        
        chunks.append((start.strftime('%Y-%m-%d'), year_end.strftime('%Y-%m-%d')))
        
        # Move to next year
        start = datetime(start.year + 1, 1, 1)
    
    return chunks

try:
    print("Authenticating with EPIAS API...")
    myTGT = sf.epias_tgt(myMail, myPsw)
    print("Authentication successful")
    
    # Get date chunks
    date_chunks = get_yearly_chunks("2023-01-01", yesterday_string)
    print(f"Retrieving demand data in {len(date_chunks)} yearly chunks:")
    for i, (chunk_start, chunk_end) in enumerate(date_chunks):
        print(f"  Chunk {i+1}: {chunk_start} to {chunk_end}")
    
    # Initialize an empty DataFrame to store all results
    all_demand_data = pd.DataFrame()
    
    # Retrieve data in chunks
    for i, (chunk_start, chunk_end) in enumerate(date_chunks):
        print(f"\nProcessing chunk {i+1}/{len(date_chunks)}: {chunk_start} to {chunk_end}")
        try:
            chunk_demand = sf.epias_demand(chunk_start, chunk_end, myTGT, myMail, myPsw)
            print(f"Retrieved {len(chunk_demand)} demand records")
            
            # Append to the main DataFrame
            if all_demand_data.empty:
                all_demand_data = chunk_demand
            else:
                all_demand_data = pd.concat([all_demand_data, chunk_demand], ignore_index=True)
            
            # Add a small delay to avoid overwhelming the API
            if i < len(date_chunks) - 1:
                print("Waiting 3 seconds before next request...")
                time.sleep(3)
                
        except Exception as e:
            print(f"Error retrieving data for chunk {chunk_start} to {chunk_end}: {e}")
            print("Traceback:")
            traceback.print_exc()
            print("Continuing with next chunk...")
    
    # Remove any potential duplicates
    all_demand_data = all_demand_data.drop_duplicates()
    
    print(f"Retrieved a total of {len(all_demand_data)} demand records from {date_chunks[0][0]} to {date_chunks[-1][1]}")
    realized_demand = all_demand_data
    
    # Check if there are datetime columns with timezone information
    datetime_columns = realized_demand.select_dtypes(include=['datetime64[ns, UTC]', 'datetime64']).columns
    
    # Convert timezone-aware datetime columns to timezone-naive
    for col in datetime_columns:
        if hasattr(realized_demand[col], 'dt') and realized_demand[col].dt.tz is not None:
            print(f"Converting timezone-aware column '{col}' to timezone-naive")
            realized_demand[col] = realized_demand[col].dt.tz_localize(None)
    
    # Create output directory if it doesn't exist
    output_dir = "../Dataset/weather"
    os.makedirs(output_dir, exist_ok=True)

    # Save to CSV in the weather dataset directory
    output_file = os.path.join(output_dir, "epias_demand_data.csv")
    realized_demand.to_csv(output_file, index=False)
    print(f"Saved demand data to {output_file}")

    # Also save backup locally for debugging
    realized_demand.to_excel("realized_demand.xlsx", index=False)
    print("Saved backup to realized_demand.xlsx")
    
    # Data processing completed - CSV file has been saved above
    print("\nDemand data processing completed successfully!")
    
    print("\nAll realized demand data has been saved to CSV file")
    print("The output contains:")
    print(f"- epias_demand_data.csv with {len(realized_demand)} rows in ../Dataset/weather/ directory")
    
    # Success
    sys.exit(0)
    
except Exception as e:
    print(f"Unhandled error: {e}")
    print("Traceback:")
    traceback.print_exc()
    sys.exit(1)
