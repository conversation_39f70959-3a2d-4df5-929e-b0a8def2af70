# Use the official Jupyter Spark notebook as base image
FROM jupyter/all-spark-notebook:spark-3.5.0

# Switch to root user to install packages
USER root

# Install system dependencies if needed
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Switch back to jovyan user for pip installations
USER jovyan

# Install Python packages
RUN pip install --no-cache-dir \
    pyspark==3.5.0 \
    mlflow

# Set working directory
WORKDIR /home/<USER>/work

# Expose ports
EXPOSE 8888 4040

# Start Jupyter notebook
CMD ["start-notebook.sh"]
