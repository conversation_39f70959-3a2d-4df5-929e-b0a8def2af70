#!/bin/bash

# Exit on error
set -e

echo "Creating conda environment 'wml' with Python 3.9..."

# Check if conda environment already exists
if conda env list | grep -q "^wml "; then
    echo "Conda environment 'wml' already exists. Removing it..."
    conda deactivate 2>/dev/null || true
    conda env remove -n wml -y
fi

# Create new conda environment with Python 3.9
conda create -n wml python=3.9 -y

# Activate the environment
echo "Activating conda environment 'wml'..."
source "$(conda info --base)/etc/profile.d/conda.sh"
conda activate wml

# Install required packages using pip
echo "Installing required packages..."
pip install pandas \
    numpy \
    requests-cache \
    retry-requests \
    openmeteo-requests \
    mysql-connector-python \
    sqlalchemy \
    pymysql \
    seffaflik2 \
    openpyxl \
    python-dotenv

echo "Environment setup complete! You can now activate it using:"
echo "conda activate wml" 