version: '3.8'

services:
  jupyter:
    build:
      context: .
      dockerfile: ./bin/jupyter.dockerfile
    container_name: jupyter-spark
    ports:
      - "8888:8888"
      - "4041:4040"
    volumes:
      - ./ml-pipelines:/home/<USER>/work
      - ./Dataset:/Dataset
    environment:
      - JUPYTER_TOKEN=password123
    depends_on:
      - spark-master
      - mlflow-tracking

  spark-master:
    image: bitnami/spark:3.5.0
    container_name: spark-master
    ports:
      - "7077:7077"
      - "8080:8080"
    environment:
      - SPARK_MODE=master
      - SPARK_RPC_AUTHENTICATION_ENABLED=no
      - SPARK_RPC_ENCRYPTION_ENABLED=no
      - SPARK_LOCAL_STORAGE_ENCRYPTION_ENABLED=no
      - SPARK_SSL_ENABLED=no
    volumes:
      - ./Dataset:/Dataset

  spark-worker:
    image: bitnami/spark:3.5.0
    container_name: spark-worker
    depends_on:
      - spark-master
    environment:
      - SPARK_MODE=worker
      - SPARK_MASTER_URL=spark://spark-master:7077
      - SPARK_WORKER_MEMORY=1G
      - SPARK_WORKER_CORES=1
      - SPARK_RPC_AUTHENTICATION_ENABLED=no
      - SPARK_RPC_ENCRYPTION_ENABLED=no
      - SPARK_LOCAL_STORAGE_ENCRYPTION_ENABLED=no
      - SPARK_SSL_ENABLED=no
    volumes:
      - ./Dataset:/Dataset

  mlflow-tracking:
    image: ghcr.io/mlflow/mlflow:v2.13.1
    ports:
      - "5000:5000"
    volumes:
      - ./mlruns:/mlflow
    command: >
      mlflow server
      --host 0.0.0.0
      --port 5000
      --backend-store-uri file:/mlflow
      --default-artifact-root file:/mlflow