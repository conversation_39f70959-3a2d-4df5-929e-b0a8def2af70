# MLflow PySpark Training Instructions

This guide provides step-by-step instructions to run ML training using MLflow orchestration with PySpark in the current Docker container setup.

## Overview

The setup includes:
- **Jupyter Notebook** with Spark support (port 8888, Spark UI on 4041)
- **Spark Master** (port 8080 for UI, 7077 for cluster)
- **Spark Worker** (connected to master)
- **MLflow Tracking Server** (port 5000)

## Prerequisites

- Docker and Docker Compose installed
- Training data available at `Dataset/train_data.csv` (Titanic dataset)
- Training script available at `notebooks/train.py`
- Custom Jupyter Dockerfile (`jupyter.dockerfile`) with pre-installed dependencies

## Step-by-Step Instructions

### 1. Build and Start the Docker Environment

```bash
# Navigate to the project directory
cd /home/<USER>/workspace/mlflow

# Build the custom Jupyter image with pre-installed dependencies
docker-compose build

# Start all services
docker-compose up -d

# Verify all containers are running
docker-compose ps
```

**Note**: The first build may take several minutes as it downloads the base image and installs all dependencies.

Expected output should show all 4 services running:
- `jupyter-spark`
- `spark-master`
- `spark-worker`
- `mlflow-tracking`

### 2. Verify Services are Running

Check that all services are accessible:

```bash
# Check MLflow tracking server
curl -f http://localhost:5000

# Check Spark Master UI
curl -f http://localhost:8080

# Check Jupyter is running
curl -f http://localhost:8888
```

### 3. Access the Services

Open the following URLs in your browser:

- **MLflow UI**: http://localhost:5000
- **Spark Master UI**: http://localhost:8080
- **Jupyter Notebook**: http://localhost:8888 (token: `password123`)

### 4. Run the Training Script

**Note**: All required dependencies (MLflow and PySpark) are now pre-installed in the custom Jupyter image, so no manual installation is needed.

#### Option A: Using Jupyter Notebook

1. Open Jupyter at http://localhost:8888
2. Use token: `password123`
3. Navigate to the `work` directory
4. Open or create a new notebook
5. Copy the contents of `train.py` or run it directly:

```python
exec(open('train.py').read())
```

#### Option B: Execute from Docker Container

```bash
# Execute the training script directly in the Jupyter container
docker exec -it jupyter-spark python /home/<USER>/work/train.py
```

#### Option C: Using Spark Submit (Alternative)

```bash
# Submit the job to Spark cluster
docker exec -it jupyter-spark spark-submit \
  --master spark://spark-master:7077 \
  --packages org.mlflow:mlflow-spark_2.12:2.13.1 \
  /home/<USER>/work/train.py
```

### 6. Monitor the Training

#### Monitor Spark Job
- Visit http://localhost:8080 to see the Spark Master UI
- Check active applications and worker status
- Monitor job progress at http://localhost:4041 (Spark Application UI)

#### Monitor MLflow Experiments
- Visit http://localhost:5000 to see the MLflow UI
- View experiment runs, metrics, and logged models
- Check the logged parameters and artifacts

### 7. Training Script Details

The `train.py` script performs:

1. **Data Loading**: Loads Titanic dataset from `/Dataset/train_data.csv`
2. **Feature Engineering**: Creates feature vectors from available columns
3. **Model Training**: Trains a Logistic Regression model using PySpark ML
4. **Evaluation**: Evaluates model using AUC metric
5. **MLflow Logging**: Logs parameters, metrics, and the trained model

Expected output:
```
Model trained with AUC: 0.7596433363148477
MLflow run ID: afedfbd0f55e42f397ce418ae4476553
🏃 View run secretive-mole-256 at: http://mlflow-tracking:5000/#/experiments/858211722605493271/runs/afedfbd0f55e42f397ce418ae4476553
🧪 View experiment at: http://mlflow-tracking:5000/#/experiments/858211722605493271
```

**Note**: The script uses only numeric features (Pclass, Age, SibSp, Parch, Fare) for training and filters out rows with missing values.

### 8. View Results

After training completion:

1. **Check MLflow UI** (http://localhost:5000):
   - View the experiment run
   - Check logged metrics (AUC)
   - Download the trained model artifacts

2. **Check Spark UI** (http://localhost:8080):
   - Verify job completion
   - Review resource utilization

### 9. Troubleshooting

#### Common Issues:

**Container startup issues:**
```bash
# Check container logs
docker-compose logs [service-name]

# Restart specific service
docker-compose restart [service-name]
```

**MLflow connection issues:**
- Ensure MLflow tracking server is running on port 5000
- Check if the tracking URI in `train.py` is correct: `http://mlflow-tracking:5000`

**Spark connection issues:**
- Verify Spark master is running on port 7077
- Check worker connection in Spark Master UI

**Data loading issues:**
- Ensure `Dataset/train_data.csv` exists and is properly mounted
- Check file permissions and format

**Dependency issues:**
- Dependencies (MLflow and PySpark) are pre-installed in the custom Docker image
- If you encounter dependency issues, rebuild the image: `docker-compose build --no-cache`

### 10. Cleanup

To stop all services:

```bash
# Stop all containers
docker-compose down

# Remove volumes (optional - will delete MLflow data)
docker-compose down -v
```

### 11. Data Information

The training data (`Dataset/train_data.csv`) contains:
- **Target variable**: `Survived` (binary classification)
- **Features**: PassengerId, Pclass, Name, Sex, Age, SibSp, Parch, Ticket, Fare, Embarked
- **Size**: 891 records
- **Type**: Titanic passenger survival dataset

## Next Steps

After successful training:
1. Experiment with different model parameters
2. Try different algorithms (Random Forest, Gradient Boosting)
3. Implement feature engineering improvements
4. Set up model serving using MLflow
5. Create automated training pipelines

## File Structure

```
mlflow/
├── docker-compose.yml          # Docker services configuration
├── jupyter.dockerfile          # Custom Jupyter image with pre-installed dependencies
├── notebooks/
│   └── train.py               # Training script
├── Dataset/
│   └── train_data.csv         # Training data
├── mlruns/                    # MLflow artifacts (created automatically)
└── instruction.md             # This file
```
