# DataDistriect Weather Energy Forecasting Pipeline

This repository contains the code and documentation for the DataDistriect weather energy forecasting pipeline. The pipeline uses machine learning to predict energy demand based on weather data from multiple Turkish cities.

## Repository Structure

The repository is organized as follows:

```
DataDistriect/
├── Collect/                   # Data collection scripts
│   ├── epias_demand_data.py   # Collects energy demand data from EPIAS API
│   ├── openmeteo_weather.py   # Collects weather data from OpenMeteo API
│   └── .env                  # Environment variables for API credentials
├── Pipeline/                 # Data processing and pipeline scripts
│   ├── data-collector/        # Data collection and validation
│   ├── ml-pipelines/          # ML training and pipeline scripts
│   └── docker-compose.yml     # Docker services configuration
├── bin/                      # Utility scripts
│   ├── conda_env_create.sh    # Creates conda environment
│   └── jupyter.dockerfile     # Custom Jupyter image with pre-installed dependencies
├── Dataset/                  # Data storage directory
│   └── weather/               # Weather and energy demand datasets
├── README.md                 # Main README file
└── weather-train.md          # Weather training instructions
```

## Getting Started

### 1. Clone the Repository

```bash
git clone https://github.com/your-repo.git
cd DataDistriect
```

### 2. Set Up Environment

#### 2.1. Create Conda Environment

```bash
# Create conda environment
bash bin/conda_env_create.sh

# Activate the environment
conda activate wml
```

#### 2.2. Install Docker

Follow the instructions on the [Docker website](https://docs.docker.com/get-docker/) to install Docker for your operating system.

### 2.3. Build Docker Images

```bash
# Build Docker images
docker-compose build
```

### 2.4. Start Docker Services

```bash
# Start Docker services
docker-compose up -d
```

### 2.5. Verify Services are Running

```bash
# Verify services are running
docker-compose ps
```

Expected output should show all services running:
- `jupyter-spark`
- `spark-master`
- `spark-worker`
- `mlflow-tracking`

## Usage


### 1. Access Jupyter Notebook

Open Jupyter Notebook at http://localhost:8888 and use the token `password123`.

### 2. Verify Services are Running

Check that all services are accessible:

```bash
# Check MLflow tracking server
curl -f http://localhost:5000

# Check Spark Master UI
curl -f http://localhost:8080

# Check Jupyter is running
curl -f http://localhost:8888
```

### 2. Collect Data

#### Configure API Credentials

Create a `.env` file in the `Collect` directory with the following content:

```bash
# EPIAS API credentials
EPIAS_EMAIL=your_email
EPIAS_PASSWORD=your_password
```

The data collection scripts are located in the `Collect` directory. Current scripts are storing data in the `Dataset/weather/` directory.

```bash
# Collect energy demand data
python Collect/epias_demand_data.py

# Collect weather data
python Collect/openmeteo_weather.py
```

### 5. Run the Training Pipeline

### Testing Pipeline
Project includes a simple testing pipeline (flight_tickets) to verify the environment is set up correctly. it uses Logistic Regression to predict flight ticket prices based on various features, and ML training is tracked by mlflow.

### to simply test the pipeline, run the following command:

```bash
# Execute the training script directly in the Jupyter container
docker exec -it jupyter-spark python /home/<USER>/work/flight_tickets/train.py
```

![alt text](./docs/images/mlflow_flights.png)

![alt text](./docs/images/spark_flights.png)


### To run the weather forcasting pipeline, run the following command:

```bash
# Execute the training script directly in the Jupyter container
docker exec -it jupyter-spark python /home/<USER>/work/weather/weather_training_pipeline.py
```


## License

This project is licensed under the MIT License.
