# Weather ML Training Instructions

This guide provides step-by-step instructions to run ML training for weather-based energy demand forecasting using MLflow orchestration with PySpark in the current Docker container setup.

## Overview

The weather training setup includes:
- **Weather datasets**: Energy demand and weather data from multiple Turkish cities
- **Feature engineering**: Time-based features, lag features, and weather aggregations
- **Multiple ML models**: LightGBM, XGBoost, LSTM, and ensemble methods
- **MLflow tracking**: Experiment tracking and model versioning
- **PySpark integration**: Distributed data processing

## Prerequisites

- Docker and Docker Compose installed and running
- Weather datasets available in `Dataset/weather/` directory:
  - `epias_demand_data.csv` - Energy demand data (21,866 records)
  - `openmeteo_weather_data.csv` - Weather data from 25 Turkish cities
  - `openmeteo_weather_summary.csv` - Weather summary statistics
- Training scripts available in `notebooks/weather/` directory:
  - `model_trainer.py` - ML model training classes
  - `feature_engineer.py` - Feature engineering utilities
  - `ensemble.py` - Model ensemble methods

## Dataset Information

### Energy Demand Data (`epias_demand_data.csv`)
- **Columns**: date, time, consumption
- **Period**: 2023 hourly data
- **Size**: 21,866 records
- **Target**: Energy consumption in MW

### Weather Data (`openmeteo_weather_data.csv`)
- **Cities**: 25 major Turkish cities (Istanbul, Ankara, Izmir, etc.)
- **Features**: Temperature, humidity, wind speed, precipitation, etc.
- **Period**: 2023 hourly data
- **Size**: 21,842 records

## Step-by-Step Training Instructions

### 1. Verify Docker Environment

Ensure the MLflow Docker environment is running:

```bash
# Navigate to the project directory
cd /home/<USER>/workspace/mlflow

# Check if services are running
docker-compose ps

# If not running, start the services
docker-compose up -d
```

### 2. Access Services

Verify all services are accessible:
- **MLflow UI**: http://localhost:5000
- **Spark Master UI**: http://localhost:8080
- **Jupyter Notebook**: http://localhost:8888 (token: `password123`)

### 3. Prepare Training Environment

#### Option A: Using Jupyter Notebook

1. Open Jupyter at http://localhost:8888
2. Use token: `password123`
3. Navigate to the `work/weather` directory
4. Create a new notebook for weather training

#### Option B: Create Training Script

Create a comprehensive training script that combines all components:

```python
# weather_training_pipeline.py
import sys
sys.path.append('/home/<USER>/work/weather')

import mlflow
import pandas as pd
import numpy as np
from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from feature_engineer import FeatureEngineer
from model_trainer import ModelTrainer
from ensemble import ModelEnsemble

# Initialize Spark
spark = SparkSession.builder \
    .appName("WeatherEnergyForecasting") \
    .config("spark.master", "spark://spark-master:7077") \
    .config("spark.sql.adaptive.enabled", "true") \
    .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
    .getOrCreate()

# Set MLflow tracking
mlflow.set_tracking_uri("http://mlflow-tracking:5000")
experiment_name = "weather-energy-forecasting"
try:
    experiment_id = mlflow.create_experiment(experiment_name)
except mlflow.exceptions.MlflowException:
    experiment_id = mlflow.get_experiment_by_name(experiment_name).experiment_id

mlflow.set_experiment(experiment_name)
```

### 4. Data Loading and Preprocessing

```python
# Load datasets
demand_df = spark.read.csv("/Dataset/weather/epias_demand_data.csv", 
                          header=True, inferSchema=True)
weather_df = spark.read.csv("/Dataset/weather/openmeteo_weather_data.csv", 
                           header=True, inferSchema=True)

# Rename columns for consistency
demand_df = demand_df.withColumnRenamed("date", "timestamp") \
                    .withColumnRenamed("consumption", "load_mw")

# Convert timestamp columns
demand_df = demand_df.withColumn("timestamp", 
                                to_timestamp(col("timestamp"), "yyyy-MM-dd HH:mm:ss"))
weather_df = weather_df.withColumn("timestamp", 
                                  to_timestamp(col("date_time"), "yyyy-MM-dd HH:mm:ss"))
```

### 5. Feature Engineering

```python
# Initialize feature engineer
fe = FeatureEngineer(spark)

# Create time-based features
demand_df = fe.create_time_features(demand_df)

# Create lag features (previous hours, days, weeks)
demand_df = fe.create_lag_features(demand_df, target_col='load_mw', 
                                  lags=[1, 2, 24, 48, 168])

# Create rolling statistics
demand_df = fe.create_rolling_features(demand_df, target_col='load_mw', 
                                      windows=[24, 48, 168])

# Process weather data (convert to city-based format and aggregate)
# Note: This requires additional preprocessing for the wide weather format
weather_agg = fe.create_weather_aggregations(weather_df)

# Join demand and weather data
final_df = demand_df.join(weather_agg, on="timestamp", how="inner")

# Remove rows with null values
final_df = final_df.dropna()
```

### 6. Model Training

```python
# Convert to Pandas for ML training (after Spark preprocessing)
train_data = final_df.toPandas()

# Prepare features and target
feature_cols = [col for col in train_data.columns 
               if col not in ['timestamp', 'load_mw', 'time']]
X = train_data[feature_cols]
y = train_data['load_mw']

# Split data (time series split)
split_date = train_data['timestamp'].quantile(0.8)
train_mask = train_data['timestamp'] <= split_date
X_train, X_val = X[train_mask], X[~train_mask]
y_train, y_val = y[train_mask], y[~train_mask]

# Initialize model trainer
trainer = ModelTrainer("http://mlflow-tracking:5000")

# Train multiple models
models = {}
metrics = {}

# LightGBM parameters
lgb_params = {
    'objective': 'regression',
    'metric': 'rmse',
    'boosting_type': 'gbdt',
    'num_leaves': 31,
    'learning_rate': 0.05,
    'feature_fraction': 0.9,
    'bagging_fraction': 0.8,
    'bagging_freq': 5,
    'verbose': -1
}

# Train LightGBM
models['lightgbm'], metrics['lightgbm'] = trainer.train_lightgbm(
    X_train, y_train, X_val, y_val, lgb_params)

# XGBoost parameters
xgb_params = {
    'objective': 'reg:squarederror',
    'eval_metric': 'rmse',
    'max_depth': 6,
    'learning_rate': 0.05,
    'subsample': 0.8,
    'colsample_bytree': 0.8,
    'verbosity': 0
}

# Train XGBoost
models['xgboost'], metrics['xgboost'] = trainer.train_xgboost(
    X_train, y_train, X_val, y_val, xgb_params)

# LSTM parameters
lstm_params = {
    'lstm_units': 50,
    'dense_units': 25,
    'dropout_rate': 0.2,
    'learning_rate': 0.001,
    'epochs': 100,
    'batch_size': 32
}

# Train LSTM
models['lstm'], metrics['lstm'] = trainer.train_lstm(
    X_train, y_train, X_val, y_val, lstm_params)
```

### 7. Model Ensemble

```python
# Create ensemble
ensemble = ModelEnsemble(list(models.values()))

# Train stacking ensemble
with mlflow.start_run(run_name="Ensemble_Stacking"):
    ensemble_pred = ensemble.train_stacking(X_train, y_train, X_val, y_val)
    
    # Calculate ensemble metrics
    from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
    ensemble_metrics = {
        'mae': mean_absolute_error(y_val, ensemble_pred),
        'rmse': np.sqrt(mean_squared_error(y_val, ensemble_pred)),
        'mape': np.mean(np.abs((y_val - ensemble_pred) / y_val)) * 100,
        'r2': r2_score(y_val, ensemble_pred)
    }
    
    # Log ensemble metrics
    mlflow.log_metrics(ensemble_metrics)
    mlflow.log_param("ensemble_type", "stacking")
    mlflow.log_param("base_models", list(models.keys()))
    
    print(f"Ensemble RMSE: {ensemble_metrics['rmse']:.2f}")
    print(f"Ensemble MAPE: {ensemble_metrics['mape']:.2f}%")
```

### 8. Execute Training

#### Option A: Run in Jupyter Notebook
Copy the above code blocks into Jupyter cells and execute sequentially.

#### Option B: Execute as Script
```bash
# Save the complete script as weather_training_pipeline.py
# Execute in the Jupyter container
docker exec -it jupyter-spark python /home/<USER>/work/weather_training_pipeline.py
```

### 9. Monitor Training Progress

#### MLflow Tracking
- Visit http://localhost:5000
- Monitor experiment runs for each model type
- Compare metrics across different models
- View feature importance and model artifacts

#### Spark Monitoring
- Visit http://localhost:8080 for Spark Master UI
- Monitor job progress at http://localhost:4041

### 10. Expected Results

The training pipeline will create multiple MLflow runs:
- **LightGBM_STLF**: Gradient boosting model run
- **XGBoost_STLF**: XGBoost model run  
- **LSTM_STLF**: Deep learning model run
- **Ensemble_Stacking**: Ensemble model run

Expected metrics:
- **RMSE**: 500-1500 MW (depending on model)
- **MAPE**: 3-8% (typical for energy forecasting)
- **R²**: 0.85-0.95 (good correlation)

### 11. Model Comparison and Selection

After training, compare models in MLflow UI:
1. Sort runs by RMSE or MAPE
2. Analyze feature importance
3. Select best performing model
4. Register model for production use

### 12. Troubleshooting

**Memory Issues:**
```bash
# Increase Spark worker memory
# Edit docker-compose.yml: SPARK_WORKER_MEMORY=2G
docker-compose down && docker-compose up -d
```

**Data Loading Issues:**
- Verify dataset files exist in `/Dataset/weather/`
- Check file permissions and CSV format
- Ensure timestamp formats are consistent

**Model Training Failures:**
- Check MLflow tracking server connectivity
- Verify all dependencies are installed
- Monitor Spark logs for memory/resource issues

### 13. Next Steps

After successful training:
1. Implement hyperparameter tuning
2. Add cross-validation for robust evaluation
3. Create automated retraining pipeline
4. Deploy best model for real-time predictions
5. Set up model monitoring and drift detection

## File Structure

```
mlflow/
├── Dataset/weather/
│   ├── epias_demand_data.csv
│   ├── openmeteo_weather_data.csv
│   └── openmeteo_weather_summary.csv
├── notebooks/weather/
│   ├── model_trainer.py
│   ├── feature_engineer.py
│   ├── ensemble.py
│   └── weather_training_pipeline.py
└── weather-train.md (this file)
```
