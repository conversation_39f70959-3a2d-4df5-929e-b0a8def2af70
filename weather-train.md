# Weather ML Training Instructions

This guide provides step-by-step instructions to run ML training for weather-based energy demand forecasting using MLflow orchestration with PySpark in the current Docker container setup.

## Overview

The weather training setup includes:
- **Weather datasets**: Energy demand and weather data from multiple Turkish cities
- **Feature engineering**: Time-based features, lag features, and weather aggregations
- **Multiple ML models**: LightGBM, XGBoost, LSTM, and ensemble methods
- **MLflow tracking**: Experiment tracking and model versioning
- **PySpark integration**: Distributed data processing

## Prerequisites

- Docker and Docker Compose installed and running
- Weather datasets available in `Dataset/weather/` directory:
  - `epias_demand_data.csv` - Energy demand data (21,866 records)
  - `openmeteo_weather_data.csv` - Weather data from 25 Turkish cities
  - `openmeteo_weather_summary.csv` - Weather summary statistics
- Training scripts available in `ml-pipelines/weather/` directory:
  - `model_trainer.py` - ML model training classes
  - `feature_engineer.py` - Feature engineering utilities
  - `ensemble.py` - Model ensemble methods

## Dataset Information

### Energy Demand Data (`epias_demand_data.csv`)
- **Columns**: date, time, consumption
- **Period**: 2023 hourly data
- **Size**: 21,866 records
- **Target**: Energy consumption in MW

### Weather Data (`openmeteo_weather_data.csv`)
- **Cities**: 25 major Turkish cities (Istanbul, Ankara, Izmir, etc.)
- **Features**: Temperature, humidity, wind speed, precipitation, etc.
- **Period**: 2023 hourly data
- **Size**: 21,842 records

## Step-by-Step Training Instructions

### 1. Verify Docker Environment

Ensure the MLflow Docker environment is running:

```bash
# Navigate to the project directory
cd ~/mlflow

# Check if services are running
docker-compose ps

# If not running, start the services
docker-compose up -d
```

### 2. Access Services

Verify all services are accessible:
- **MLflow UI**: http://localhost:5000
- **Spark Master UI**: http://localhost:8080
- **Jupyter Notebook**: http://localhost:8888 (token: `password123`)

### 3. Prepare Training Environment

#### Option A: Using Jupyter Notebook

1. Open Jupyter at http://localhost:8888
2. Use token: `password123`
3. Navigate to the `work/weather` directory
4. Create a new notebook for weather training


### 8. Execute Training

#### Option A: Run in Jupyter Notebook
Copy the above code blocks into Jupyter cells and execute sequentially.

#### Option B: Execute as Script
```bash
# Save the complete script as weather_training_pipeline.py
# Execute in the Jupyter container
docker exec -it jupyter-spark python /home/<USER>/work/weather/weather_training_pipeline.py
```

### 9. Monitor Training Progress

#### MLflow Tracking
- Visit http://localhost:5000
- Monitor experiment runs for each model type
- Compare metrics across different models
- View feature importance and model artifacts

#### Spark Monitoring
- Visit http://localhost:8080 for Spark Master UI
- Monitor job progress at http://localhost:4041

### 10. Expected Results

The training pipeline will create multiple MLflow runs:
- **LightGBM_STLF**: Gradient boosting model run
- **XGBoost_STLF**: XGBoost model run  
- **LSTM_STLF**: Deep learning model run
- **Ensemble_Stacking**: Ensemble model run

Expected metrics:
- **RMSE**: 500-1500 MW (depending on model)
- **MAPE**: 3-8% (typical for energy forecasting)
- **R²**: 0.85-0.95 (good correlation)

### 11. Model Comparison and Selection

After training, compare models in MLflow UI:
1. Sort runs by RMSE or MAPE
2. Analyze feature importance
3. Select best performing model
4. Register model for production use

### 12. Troubleshooting

**Memory Issues:**
```bash
# Increase Spark worker memory
# Edit docker-compose.yml: SPARK_WORKER_MEMORY=2G
docker-compose down && docker-compose up -d
```

**Data Loading Issues:**
- Verify dataset files exist in `/Dataset/weather/`
- Check file permissions and CSV format
- Ensure timestamp formats are consistent

**Model Training Failures:**
- Check MLflow tracking server connectivity
- Verify all dependencies are installed
- Monitor Spark logs for memory/resource issues

### 13. Next Steps

After successful training:
1. Implement hyperparameter tuning
2. Add cross-validation for robust evaluation
3. Create automated retraining pipeline
4. Deploy best model for real-time predictions
5. Set up model monitoring and drift detection

## File Structure

```
mlflow/
├── Dataset/weather/
│   ├── epias_demand_data.csv
│   ├── openmeteo_weather_data.csv
│   └── openmeteo_weather_summary.csv
├── notebooks/weather/
│   ├── model_trainer.py
│   ├── feature_engineer.py
│   ├── ensemble.py
│   └── weather_training_pipeline.py
└── weather-train.md (this file)
```
