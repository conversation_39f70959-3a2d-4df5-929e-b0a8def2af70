#!/usr/bin/env python3
"""
Simple script to run the weather training pipeline with proper error handling
"""

import sys
import os
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path for imports
sys.path.append('/home/<USER>/work/weather')
sys.path.append('/home/<USER>/work')

def main():
    """Run the weather training pipeline"""
    print("="*60)
    print("WEATHER ENERGY FORECASTING TRAINING PIPELINE")
    print("="*60)
    
    try:
        # Import and run the pipeline
        from weather_training_pipeline import WeatherEnergyPipeline
        
        print("Initializing pipeline...")
        pipeline = WeatherEnergyPipeline()
        
        print("Starting training pipeline...")
        pipeline.run_pipeline()
        
        print("\n" + "="*60)
        print("✅ PIPELINE COMPLETED SUCCESSFULLY!")
        print("="*60)
        print("\nNext steps:")
        print("1. Check MLflow UI at http://localhost:5000")
        print("2. Review model performance metrics")
        print("3. Select best performing model for deployment")
        print("4. Consider hyperparameter tuning for better results")
        
    except KeyboardInterrupt:
        print("\n❌ Pipeline interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Pipeline failed with error: {str(e)}")
        print("\nTroubleshooting tips:")
        print("1. Ensure all Docker services are running:")
        print("   docker ps")
        print("2. Check MLflow server connectivity:")
        print("   curl http://localhost:5000")
        print("3. Verify data files exist in /Dataset/weather/")
        print("4. Check Spark cluster status at http://localhost:8080")
        sys.exit(1)

if __name__ == "__main__":
    main()
