#!/usr/bin/env python3
"""
Quick test to verify the weather training pipeline works with a small dataset
"""

import sys
import os
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path for imports
sys.path.append('/home/<USER>/work/weather')
sys.path.append('/home/<USER>/work')

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score

def test_basic_functionality():
    """Test basic ML functionality with synthetic data"""
    print("Testing basic ML functionality...")
    
    # Create synthetic data
    np.random.seed(42)
    n_samples = 1000
    n_features = 10
    
    X = np.random.randn(n_samples, n_features)
    y = X[:, 0] * 2 + X[:, 1] * 1.5 + np.random.randn(n_samples) * 0.1
    
    # Convert to DataFrame
    feature_names = [f'feature_{i}' for i in range(n_features)]
    X_df = pd.DataFrame(X, columns=feature_names)
    
    # Split data
    split_idx = int(0.8 * n_samples)
    X_train, X_val = X_df[:split_idx], X_df[split_idx:]
    y_train, y_val = y[:split_idx], y[split_idx:]
    
    # Train model
    model = RandomForestRegressor(n_estimators=10, random_state=42)
    model.fit(X_train, y_train)
    
    # Make predictions
    y_pred = model.predict(X_val)
    
    # Calculate metrics
    mae = mean_absolute_error(y_val, y_pred)
    rmse = np.sqrt(mean_squared_error(y_val, y_pred))
    r2 = r2_score(y_val, y_pred)
    
    print(f"✓ Basic ML test passed:")
    print(f"  MAE: {mae:.4f}")
    print(f"  RMSE: {rmse:.4f}")
    print(f"  R²: {r2:.4f}")
    
    return True

def test_data_loading():
    """Test if weather data files exist and can be loaded"""
    print("\nTesting data loading...")
    
    try:
        # Check if files exist
        demand_file = "/Dataset/weather/epias_demand_data.csv"
        weather_file = "/Dataset/weather/openmeteo_weather_data.csv"
        
        if not os.path.exists(demand_file):
            print(f"❌ Demand data file not found: {demand_file}")
            return False
            
        if not os.path.exists(weather_file):
            print(f"❌ Weather data file not found: {weather_file}")
            return False
        
        # Try to load a few rows
        demand_df = pd.read_csv(demand_file, nrows=5)
        weather_df = pd.read_csv(weather_file, nrows=5)
        
        print(f"✓ Data files found and readable:")
        print(f"  Demand data columns: {list(demand_df.columns)}")
        print(f"  Weather data shape: {weather_df.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Data loading failed: {str(e)}")
        return False

def test_model_trainer():
    """Test ModelTrainer with synthetic data"""
    print("\nTesting ModelTrainer...")
    
    try:
        from model_trainer import ModelTrainer
        
        # Create synthetic data
        np.random.seed(42)
        n_samples = 100
        n_features = 5
        
        X = np.random.randn(n_samples, n_features)
        y = X[:, 0] * 2 + X[:, 1] * 1.5 + np.random.randn(n_samples) * 0.1
        
        # Convert to DataFrame
        feature_names = [f'feature_{i}' for i in range(n_features)]
        X_df = pd.DataFrame(X, columns=feature_names)
        
        # Split data
        split_idx = int(0.8 * n_samples)
        X_train, X_val = X_df[:split_idx], X_df[split_idx:]
        y_train, y_val = y[:split_idx], y[split_idx:]
        
        # Test sklearn model training (should always work)
        trainer = ModelTrainer("http://mlflow-tracking:5000")
        
        rf_params = {'n_estimators': 10, 'random_state': 42}
        model, metrics = trainer.train_sklearn_model(
            X_train, y_train, X_val, y_val, 'RandomForest', rf_params)
        
        print(f"✓ ModelTrainer test passed:")
        print(f"  RMSE: {metrics['rmse']:.4f}")
        print(f"  R²: {metrics['r2']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ ModelTrainer test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("Running quick tests for weather training pipeline...\n")
    
    tests = [
        test_basic_functionality,
        test_data_loading,
        test_model_trainer
    ]
    
    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")
    
    print(f"\n{'='*50}")
    print(f"Tests passed: {passed}/{len(tests)}")
    
    if passed == len(tests):
        print("✅ All tests passed! The pipeline should work correctly.")
        print("\nTo run the full pipeline:")
        print("python /home/<USER>/work/weather/weather_training_pipeline.py")
    else:
        print("❌ Some tests failed. Please check the issues above.")
    print("="*50)
