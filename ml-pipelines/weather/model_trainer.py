# services/ml-pipeline/src/models/model_trainer.py
import mlflow
import mlflow.sklearn
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, <PERSON>, Lasso
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import pandas as pd
import numpy as np
from typing import Dict, Any, Tuple
import logging

# Optional imports with graceful fallback
try:
    import xgboost as xgb
    import mlflow.xgboost
    HAS_XGBOOST = True
except ImportError:
    HAS_XGBOOST = False

try:
    import lightgbm as lgb
    import mlflow.lightgbm
    HAS_LIGHTGBM = True
except ImportError:
    HAS_LIGHTGBM = False

try:
    import tensorflow as tf
    HAS_TENSORFLOW = True
except ImportError:
    HAS_TENSORFLOW = False


class ModelTrainer:
    def __init__(self, mlflow_tracking_uri: str):
        mlflow.set_tracking_uri(mlflow_tracking_uri)
        self.logger = logging.getLogger(__name__)

    def train_sklearn_model(self, X_train, y_train, X_val, y_val, model_name: str,
                           model_params: Dict[str, Any]) -> Tuple[Any, Dict[str, float]]:
        """Train scikit-learn model (fallback for missing libraries)"""
        with mlflow.start_run(run_name=f"{model_name}_STLF"):
            # Select model based on name
            if model_name.lower() == 'randomforest':
                model = RandomForestRegressor(**model_params)
            elif model_name.lower() == 'gradientboosting':
                model = GradientBoostingRegressor(**model_params)
            elif model_name.lower() == 'ridge':
                model = Ridge(**model_params)
            elif model_name.lower() == 'lasso':
                model = Lasso(**model_params)
            else:
                model = LinearRegression(**model_params)

            # Log parameters
            mlflow.log_params(model_params)
            mlflow.log_param("model_type", model_name)

            # Train model
            model.fit(X_train, y_train)

            # Make predictions
            y_pred = model.predict(X_val)

            # Calculate metrics
            metrics = self._calculate_metrics(y_val, y_pred)

            # Log metrics
            mlflow.log_metrics(metrics)

            # Log model
            mlflow.sklearn.log_model(model, "model")

            # Log feature importance if available
            if hasattr(model, 'feature_importances_'):
                feature_importance = pd.DataFrame({
                    'feature': X_train.columns,
                    'importance': model.feature_importances_
                }).sort_values('importance', ascending=False)
                mlflow.log_text(feature_importance.to_string(), "feature_importance.txt")

            return model, metrics

    def train_lightgbm(self, X_train, y_train, X_val, y_val,
                      params: Dict[str, Any]) -> Tuple[Any, Dict[str, float]]:
        """Train LightGBM model or fallback to GradientBoosting"""
        if not HAS_LIGHTGBM:
            self.logger.warning("LightGBM not available, using GradientBoostingRegressor")
            fallback_params = {
                'n_estimators': params.get('num_boost_round', 100),
                'learning_rate': params.get('learning_rate', 0.1),
                'max_depth': params.get('max_depth', 6),
                'random_state': 42
            }
            return self.train_sklearn_model(X_train, y_train, X_val, y_val,
                                          'GradientBoosting', fallback_params)

        with mlflow.start_run(run_name="LightGBM_STLF"):
            # Log parameters
            mlflow.log_params(params)
            
            # Prepare datasets
            train_data = lgb.Dataset(X_train, label=y_train)
            val_data = lgb.Dataset(X_val, label=y_val)
            
            # Train model
            model = lgb.train(
                params,
                train_data,
                valid_sets=[train_data, val_data],
                valid_names=['train', 'eval'],
                num_boost_round=1000,
                callbacks=[lgb.early_stopping(50), lgb.log_evaluation(100)]
            )
            
            # Make predictions
            y_pred = model.predict(X_val)
            
            # Calculate metrics
            metrics = self._calculate_metrics(y_val, y_pred)
            
            # Log metrics
            mlflow.log_metrics(metrics)
            
            # Log model
            mlflow.lightgbm.log_model(model, "model")
            
            # Log feature importance
            feature_importance = pd.DataFrame({
                'feature': X_train.columns,
                'importance': model.feature_importance()
            }).sort_values('importance', ascending=False)
            
            mlflow.log_text(feature_importance.to_string(), "feature_importance.txt")
            
            return model, metrics
    
    def train_xgboost(self, X_train, y_train, X_val, y_val,
                     params: Dict[str, Any]) -> Tuple[Any, Dict[str, float]]:
        """Train XGBoost model or fallback to RandomForest"""
        if not HAS_XGBOOST:
            self.logger.warning("XGBoost not available, using RandomForestRegressor")
            fallback_params = {
                'n_estimators': params.get('num_boost_round', 100),
                'max_depth': params.get('max_depth', 6),
                'random_state': 42
            }
            return self.train_sklearn_model(X_train, y_train, X_val, y_val,
                                          'RandomForest', fallback_params)

        with mlflow.start_run(run_name="XGBoost_STLF"):
            # Log parameters
            mlflow.log_params(params)
            
            # Prepare datasets
            dtrain = xgb.DMatrix(X_train, label=y_train)
            dval = xgb.DMatrix(X_val, label=y_val)
            
            # Train model
            model = xgb.train(
                params,
                dtrain,
                num_boost_round=1000,
                evals=[(dtrain, 'train'), (dval, 'eval')],
                early_stopping_rounds=50,
                verbose_eval=100
            )
            
            # Make predictions
            y_pred = model.predict(dval)
            
            # Calculate metrics
            metrics = self._calculate_metrics(y_val, y_pred)
            
            # Log metrics
            mlflow.log_metrics(metrics)
            
            # Log model
            mlflow.xgboost.log_model(model, "model")
            
            return model, metrics
    
    def train_lstm(self, X_train, y_train, X_val, y_val,
                  params: Dict[str, Any]) -> Tuple[Any, Dict[str, float]]:
        """Train LSTM model or fallback to Ridge regression"""
        if not HAS_TENSORFLOW:
            self.logger.warning("TensorFlow not available, using Ridge regression")
            fallback_params = {
                'alpha': 1.0,
                'random_state': 42
            }
            return self.train_sklearn_model(X_train, y_train, X_val, y_val,
                                          'Ridge', fallback_params)

        import tensorflow as tf
        from tensorflow.keras.models import Sequential
        from tensorflow.keras.layers import LSTM, Dense, Dropout
        from tensorflow.keras.callbacks import EarlyStopping
        
        with mlflow.start_run(run_name="LSTM_STLF"):
            # Log parameters
            mlflow.log_params(params)
            
            # Reshape data for LSTM (samples, timesteps, features)
            X_train_lstm = X_train.values.reshape((X_train.shape[0], 1, X_train.shape[1]))
            X_val_lstm = X_val.values.reshape((X_val.shape[0], 1, X_val.shape[1]))
            
            # Build model
            model = Sequential([
                LSTM(params['lstm_units'], activation='relu', input_shape=(1, X_train.shape[1])),
                Dropout(params['dropout_rate']),
                Dense(params['dense_units'], activation='relu'),
                Dense(1)
            ])
            
            model.compile(
                optimizer=tf.keras.optimizers.Adam(learning_rate=params['learning_rate']),
                loss='mse',
                metrics=['mae']
            )
            
            # Train model
            early_stopping = EarlyStopping(patience=20, restore_best_weights=True)
            
            history = model.fit(
                X_train_lstm, y_train,
                epochs=params['epochs'],
                batch_size=params['batch_size'],
                validation_data=(X_val_lstm, y_val),
                callbacks=[early_stopping],
                verbose=1
            )
            
            # Make predictions
            y_pred = model.predict(X_val_lstm).flatten()
            
            # Calculate metrics
            metrics = self._calculate_metrics(y_val, y_pred)
            
            # Log metrics
            mlflow.log_metrics(metrics)
            
            # Log model
            mlflow.tensorflow.log_model(model, "model")
            
            return model, metrics
    
    def _calculate_metrics(self, y_true, y_pred) -> Dict[str, float]:
        """Calculate evaluation metrics"""
        return {
            'mae': mean_absolute_error(y_true, y_pred),
            'rmse': np.sqrt(mean_squared_error(y_true, y_pred)),
            'mape': np.mean(np.abs((y_true - y_pred) / y_true)) * 100,
            'r2': r2_score(y_true, y_pred)
        }