# services/dashboard/src/app.py
import dash
from dash import dcc, html, Input, Output, callback
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
import boto3
import mlflow
from datetime import datetime, timedelta
import io

class ElectricityDashboard:
    def __init__(self):
        self.app = dash.Dash(__name__)
        self.s3_client = boto3.client('s3')
        self.mlflow_client = mlflow.tracking.MlflowClient()
        self.setup_layout()
        self.setup_callbacks()
    
    def setup_layout(self):
        self.app.layout = html.Div([
            html.H1("Turkey Electricity Load Prediction Dashboard", 
                   style={'textAlign': 'center', 'marginBottom': 30}),
            
            # Control Panel
            html.Div([
                html.Div([
                    html.Label("Time Range:"),
                    dcc.DatePickerRange(
                        id='date-range-picker',
                        start_date=datetime.now() - timedelta(days=7),
                        end_date=datetime.now() + timedelta(days=1),
                        display_format='YYYY-MM-DD'
                    )
                ], style={'width': '30%', 'display': 'inline-block'}),
                
                html.Div([
                    html.Label("Model:"),
                    dcc.Dropdown(
                        id='model-dropdown',
                        options=[
                            {'label': 'LightGBM', 'value': 'lightgbm'},
                            {'label': 'XGBoost', 'value': 'xgboost'},
                            {'label': 'LSTM', 'value': 'lstm'},
                            {'label': 'Ensemble', 'value': 'ensemble'}
                        ],
                        value='ensemble'
                    )
                ], style={'width': '30%',