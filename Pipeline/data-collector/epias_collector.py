# services/data-collector/src/collectors/pias_collector.py
import requests
import pandas as pd
import boto3
from datetime import datetime, timedelta
import logging
from typing import Dict, List
import time

class PiasDataCollector:
    def __init__(self, api_key: str, s3_bucket: str):
        self.api_key = api_key
        self.s3_bucket = s3_bucket
        self.s3_client = boto3.client('s3')
        self.logger = logging.getLogger(__name__)
        
    def fetch_electricity_load(self, start_date: str, end_date: str) -> pd.DataFrame:
        """Fetch electricity load data from PİAŞ API"""
        url = "https://seffaflik.epias.com.tr/transparency/service/consumption/real-time-consumption"
        
        params = {
            'startDate': start_date,
            'endDate': end_date,
            'region': 'TR1'  # Turkey
        }
        
        headers = {
            'TGT': self.api_key,
            'Content-Type': 'application/json'
        }
        
        try:
            response = requests.get(url, params=params, headers=headers)
            response.raise_for_status()
            
            data = response.json()
            df = pd.DataFrame(data['body']['consumptionList'])
            df['timestamp'] = pd.to_datetime(df['date'])
            df['load_mw'] = df['consumption'].astype(float)
            
            return df[['timestamp', 'load_mw']]
            
        except Exception as e:
            self.logger.error(f"Error fetching PİAŞ data: {e}")
            raise
    
    def save_to_s3(self, df: pd.DataFrame, key: str):
        """Save DataFrame to S3 as parquet"""
        try:
            buffer = io.BytesIO()
            df.to_parquet(buffer, index=False)
            buffer.seek(0)
            
            self.s3_client.put_object(
                Bucket=self.s3_bucket,
                Key=key,
                Body=buffer.getvalue()
            )
            
            self.logger.info(f"Data saved to s3://{self.s3_bucket}/{key}")
            
        except Exception as e:
            self.logger.error(f"Error saving to S3: {e}")
            raise
