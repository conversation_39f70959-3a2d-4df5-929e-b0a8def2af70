# services/data-collector/src/validation/data_validator.py
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import logging

class DataValidator:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def validate_load_data(self, df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate electricity load data"""
        issues = []
        
        # Check for missing values
        if df['load_mw'].isnull().any():
            null_count = df['load_mw'].isnull().sum()
            issues.append(f"Found {null_count} null values in load_mw")
        
        # Check for negative values
        if (df['load_mw'] < 0).any():
            neg_count = (df['load_mw'] < 0).sum()
            issues.append(f"Found {neg_count} negative values in load_mw")
        
        # Check for unrealistic values (Turkey's typical range: 15,000-50,000 MW)
        outliers = df[(df['load_mw'] < 10000) | (df['load_mw'] > 60000)]
        if len(outliers) > 0:
            issues.append(f"Found {len(outliers)} outlier values outside reasonable range")
        
        # Check timestamp continuity
        df_sorted = df.sort_values('timestamp')
        time_diffs = df_sorted['timestamp'].diff()[1:]
        expected_diff = pd.Timedelta(hours=1)
        
        irregular_intervals = time_diffs[time_diffs != expected_diff]
        if len(irregular_intervals) > 0:
            issues.append(f"Found {len(irregular_intervals)} irregular time intervals")
        
        return len(issues) == 0, issues
    
    def validate_weather_data(self, df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate weather data"""
        issues = []
        
        # Temperature range checks
        temp_outliers = df[(df['temperature'] < -30) | (df['temperature'] > 55)]
        if len(temp_outliers) > 0:
            issues.append(f"Found {len(temp_outliers)} temperature outliers")
        
        # Humidity range checks
        humidity_outliers = df[(df['humidity'] < 0) | (df['humidity'] > 100)]
        if len(humidity_outliers) > 0:
            issues.append(f"Found {len(humidity_outliers)} humidity outliers")
        
        # Wind speed checks
        wind_outliers = df[df['wind_speed'] < 0]
        if len(wind_outliers) > 0:
            issues.append(f"Found {len(wind_outliers)} negative wind speed values")
        
        return len(issues) == 0, issues