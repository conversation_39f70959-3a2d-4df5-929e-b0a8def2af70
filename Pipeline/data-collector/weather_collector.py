import requests
import pandas as pd
import boto3
from datetime import datetime, timedelta
import logging
from typing import Dict, List
import time



class WeatherDataCollector:
    def __init__(self, s3_bucket: str):
        self.s3_bucket = s3_bucket
        self.s3_client = boto3.client('s3')
        self.logger = logging.getLogger(__name__)
        
        # Major Turkish cities coordinates
        self.cities = {
            'istanbul': {'lat': 41.0082, 'lon': 28.9784},
            'ankara': {'lat': 39.9334, 'lon': 32.8597},
            'izmir': {'lat': 38.4192, 'lon': 27.1287},
            'bursa': {'lat': 40.1826, 'lon': 29.0665},
            'antalya': {'lat': 36.8969, 'lon': 30.7133}
        }
    
    def fetch_weather_forecast(self, city: str, forecast_days: int = 7) -> pd.DataFrame:
        """Fetch weather forecast from OpenMeteo API"""
        coords = self.cities[city]
        
        url = "https://api.open-meteo.com/v1/forecast"
        params = {
            'latitude': coords['lat'],
            'longitude': coords['lon'],
            'hourly': [
                'temperature_2m',
                'apparent_temperature',
                'relative_humidity_2m',
                'wind_speed_10m',
                'cloud_cover',
                'solar_radiation'
            ],
            'forecast_days': forecast_days,
            'timezone': 'Europe/Istanbul'
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            hourly = data['hourly']
            
            df = pd.DataFrame({
                'timestamp': pd.to_datetime(hourly['time']),
                'city': city,
                'temperature': hourly['temperature_2m'],
                'apparent_temperature': hourly['apparent_temperature'],
                'humidity': hourly['relative_humidity_2m'],
                'wind_speed': hourly['wind_speed_10m'],
                'cloud_cover': hourly['cloud_cover'],
                'solar_radiation': hourly['solar_radiation']
            })
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error fetching weather data for {city}: {e}")
            raise