# services/ml-pipeline/src/models/model_trainer.py
import mlflow
import mlflow.sklearn
import mlflow.xgboost
import mlflow.lightgbm
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import xgboost as xgb
import lightgbm as lgb
import pandas as pd
import numpy as np
from typing import Dict, Any, Tuple
import logging

class ModelTrainer:
    def __init__(self, mlflow_tracking_uri: str):
        mlflow.set_tracking_uri(mlflow_tracking_uri)
        self.logger = logging.getLogger(__name__)
        
    def train_lightgbm(self, X_train, y_train, X_val, y_val, params: Dict[str, Any]) -> Tuple[Any, Dict[str, float]]:
        """Train LightGBM model"""
        with mlflow.start_run(run_name="LightGBM_STLF"):
            # Log parameters
            mlflow.log_params(params)
            
            # Prepare datasets
            train_data = lgb.Dataset(X_train, label=y_train)
            val_data = lgb.Dataset(X_val, label=y_val)
            
            # Train model
            model = lgb.train(
                params,
                train_data,
                valid_sets=[train_data, val_data],
                valid_names=['train', 'eval'],
                num_boost_round=1000,
                callbacks=[lgb.early_stopping(50), lgb.log_evaluation(100)]
            )
            
            # Make predictions
            y_pred = model.predict(X_val)
            
            # Calculate metrics
            metrics = self._calculate_metrics(y_val, y_pred)
            
            # Log metrics
            mlflow.log_metrics(metrics)
            
            # Log model
            mlflow.lightgbm.log_model(model, "model")
            
            # Log feature importance
            feature_importance = pd.DataFrame({
                'feature': X_train.columns,
                'importance': model.feature_importance()
            }).sort_values('importance', ascending=False)
            
            mlflow.log_text(feature_importance.to_string(), "feature_importance.txt")
            
            return model, metrics
    
    def train_xgboost(self, X_train, y_train, X_val, y_val, params: Dict[str, Any]) -> Tuple[Any, Dict[str, float]]:
        """Train XGBoost model"""
        with mlflow.start_run(run_name="XGBoost_STLF"):
            # Log parameters
            mlflow.log_params(params)
            
            # Prepare datasets
            dtrain = xgb.DMatrix(X_train, label=y_train)
            dval = xgb.DMatrix(X_val, label=y_val)
            
            # Train model
            model = xgb.train(
                params,
                dtrain,
                num_boost_round=1000,
                evals=[(dtrain, 'train'), (dval, 'eval')],
                early_stopping_rounds=50,
                verbose_eval=100
            )
            
            # Make predictions
            y_pred = model.predict(dval)
            
            # Calculate metrics
            metrics = self._calculate_metrics(y_val, y_pred)
            
            # Log metrics
            mlflow.log_metrics(metrics)
            
            # Log model
            mlflow.xgboost.log_model(model, "model")
            
            return model, metrics
    
    def train_lstm(self, X_train, y_train, X_val, y_val, params: Dict[str, Any]) -> Tuple[Any, Dict[str, float]]:
        """Train LSTM model"""
        import tensorflow as tf
        from tensorflow.keras.models import Sequential
        from tensorflow.keras.layers import LSTM, Dense, Dropout
        from tensorflow.keras.callbacks import EarlyStopping
        
        with mlflow.start_run(run_name="LSTM_STLF"):
            # Log parameters
            mlflow.log_params(params)
            
            # Reshape data for LSTM (samples, timesteps, features)
            X_train_lstm = X_train.values.reshape((X_train.shape[0], 1, X_train.shape[1]))
            X_val_lstm = X_val.values.reshape((X_val.shape[0], 1, X_val.shape[1]))
            
            # Build model
            model = Sequential([
                LSTM(params['lstm_units'], activation='relu', input_shape=(1, X_train.shape[1])),
                Dropout(params['dropout_rate']),
                Dense(params['dense_units'], activation='relu'),
                Dense(1)
            ])
            
            model.compile(
                optimizer=tf.keras.optimizers.Adam(learning_rate=params['learning_rate']),
                loss='mse',
                metrics=['mae']
            )
            
            # Train model
            early_stopping = EarlyStopping(patience=20, restore_best_weights=True)
            
            history = model.fit(
                X_train_lstm, y_train,
                epochs=params['epochs'],
                batch_size=params['batch_size'],
                validation_data=(X_val_lstm, y_val),
                callbacks=[early_stopping],
                verbose=1
            )
            
            # Make predictions
            y_pred = model.predict(X_val_lstm).flatten()
            
            # Calculate metrics
            metrics = self._calculate_metrics(y_val, y_pred)
            
            # Log metrics
            mlflow.log_metrics(metrics)
            
            # Log model
            mlflow.tensorflow.log_model(model, "model")
            
            return model, metrics
    
    def _calculate_metrics(self, y_true, y_pred) -> Dict[str, float]:
        """Calculate evaluation metrics"""
        return {
            'mae': mean_absolute_error(y_true, y_pred),
            'rmse': np.sqrt(mean_squared_error(y_true, y_pred)),
            'mape': np.mean(np.abs((y_true - y_pred) / y_true)) * 100,
            'r2': r2_score(y_true, y_pred)
        }