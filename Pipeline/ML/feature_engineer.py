# services/ml-pipeline/src/features/feature_engineer.py
import pandas as pd
import numpy as np
from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
import holidays

class FeatureEngineer:
    def __init__(self, spark: SparkSession):
        self.spark = spark
        self.turkish_holidays = holidays.Turkey()
    
    def create_time_features(self, df):
        """Create time-based features"""
        df = df.withColumn('hour', hour('timestamp'))
        df = df.withColumn('day_of_week', dayofweek('timestamp'))
        df = df.withColumn('month', month('timestamp'))
        df = df.withColumn('quarter', quarter('timestamp'))
        df = df.withColumn('year', year('timestamp'))
        
        # Weekend indicator
        df = df.withColumn('is_weekend', 
                          when(col('day_of_week').isin([1, 7]), 1).otherwise(0))
        
        # Working hours indicator
        df = df.withColumn('is_working_hours',
                          when((col('hour') >= 8) & (col('hour') <= 18), 1).otherwise(0))
        
        return df
    
    def create_lag_features(self, df, target_col='load_mw', lags=[1, 2, 24, 48, 168]):
        """Create lag features for time series"""
        window_spec = Window.orderBy('timestamp')
        
        for lag in lags:
            df = df.withColumn(f'{target_col}_lag_{lag}',
                             lag(col(target_col), lag).over(window_spec))
        
        return df
    
    def create_rolling_features(self, df, target_col='load_mw', windows=[24, 48, 168]):
        """Create rolling statistics features"""
        for window in windows:
            window_spec = Window.orderBy('timestamp').rowsBetween(-window, -1)
            
            df = df.withColumn(f'{target_col}_roll_mean_{window}',
                             avg(col(target_col)).over(window_spec))
            
            df = df.withColumn(f'{target_col}_roll_std_{window}',
                             stddev(col(target_col)).over(window_spec))
            
            df = df.withColumn(f'{target_col}_roll_min_{window}',
                             min(col(target_col)).over(window_spec))
            
            df = df.withColumn(f'{target_col}_roll_max_{window}',
                             max(col(target_col)).over(window_spec))
        
        return df
    
    def create_weather_aggregations(self, weather_df):
        """Aggregate weather data across cities"""
        # Population-weighted averages (approximate weights)
        city_weights = {
            'istanbul': 0.18,
            'ankara': 0.06,
            'izmir': 0.05,
            'bursa': 0.03,
            'antalya': 0.02
        }
        
        # Add weights
        for city, weight in city_weights.items():
            weather_df = weather_df.withColumn('weight',
                                             when(col('city') == city, weight).otherwise(0))
        
        # Weighted aggregations
        agg_df = weather_df.groupBy('timestamp').agg(
            sum(col('temperature') * col('weight')).alias('weighted_avg_temp'),
            sum(col('apparent_temperature') * col('weight')).alias('weighted_avg_apparent_temp'),
            sum(col('humidity') * col('weight')).alias('weighted_avg_humidity'),
            sum(col('wind_speed') * col('weight')).alias('weighted_avg_wind_speed'),
            sum(col('cloud_cover') * col('weight')).alias('weighted_avg_cloud_cover'),
            sum(col('solar_radiation') * col('weight')).alias('weighted_avg_solar_radiation')
        )
        
        return agg_df